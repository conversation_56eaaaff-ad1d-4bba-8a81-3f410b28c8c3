"use client"

import { useState, useEffect } from 'react'
import { ArrowLeft, Search, Filter, RefreshCw } from 'lucide-react'
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { Order, Dealer } from '@/lib/types'
import { orderApi, dealerApi } from '@/lib/api'

export default function OrderSwitchPage() {
  const [orders, setOrders] = useState<Order[]>([])
  const [dealers, setDealers] = useState<Dealer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchKeyword, setSearchKeyword] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [selectedDealer, setSelectedDealer] = useState<string>('')
  const [switching, setSwitching] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const { toast } = useToast()

  // 获取订单状态显示文本
  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: '待确认',
      confirmed: '已确认',
      processing: '处理中',
      shipped: '已发货',
      delivered: '已送达',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-blue-100 text-blue-800',
      processing: 'bg-purple-100 text-purple-800',
      shipped: 'bg-green-100 text-green-800',
      delivered: 'bg-gray-100 text-gray-800',
      cancelled: 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const [ordersResponse, dealersResponse] = await Promise.all([
        orderApi.getOrders({ 
          keyword: searchKeyword, 
          status: statusFilter,
          page: 1, 
          pageSize: 50 
        }),
        dealerApi.getDealers({ keyword: '' })
      ])

      if (ordersResponse.success && ordersResponse.data) {
        setOrders(ordersResponse.data.orders)
      }

      if (dealersResponse.success && dealersResponse.data) {
        setDealers(dealersResponse.data)
      }
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载数据，请稍后重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理搜索
  const handleSearch = () => {
    loadData()
  }

  // 处理订单切换
  const handleSwitchOrder = async () => {
    if (!selectedOrder || !selectedDealer) return

    setSwitching(true)
    try {
      const response = await orderApi.switchOrderDealer(selectedOrder.id, selectedDealer)
      
      if (response.success) {
        toast({
          title: '切换成功',
          description: response.message || '订单经销商已成功切换'
        })
        setDialogOpen(false)
        setSelectedOrder(null)
        setSelectedDealer('')
        loadData() // 重新加载数据
      } else {
        toast({
          title: '切换失败',
          description: response.error || '切换过程中发生错误',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: '切换失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive'
      })
    } finally {
      setSwitching(false)
    }
  }

  // 打开切换对话框
  const openSwitchDialog = (order: Order) => {
    setSelectedOrder(order)
    setSelectedDealer('')
    setDialogOpen(true)
  }

  useEffect(() => {
    loadData()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center">
        <Link href="/">
          <ArrowLeft className="w-6 h-6 mr-4" />
        </Link>
        <h1 className="text-xl font-semibold">订单切换</h1>
      </div>

      <div className="p-4 space-y-4">
        {/* 搜索和筛选 */}
        <div className="space-y-3">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <Input
                placeholder="搜索订单号、客户名称或经销商"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} size="icon">
              <Search className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="pending">待确认</SelectItem>
                <SelectItem value="confirmed">已确认</SelectItem>
                <SelectItem value="processing">处理中</SelectItem>
                <SelectItem value="shipped">已发货</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" onClick={loadData} size="icon">
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 订单列表 */}
        {loading ? (
          <div className="text-center py-8">
            <div className="text-gray-500">加载中...</div>
          </div>
        ) : (
          <div className="space-y-3">
            {orders.map((order) => (
              <Card key={order.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="font-medium text-lg">{order.orderNumber}</div>
                      <div className="text-sm text-gray-600">客户: {order.customerName}</div>
                    </div>
                    <Badge className={getStatusColor(order.status)}>
                      {getStatusText(order.status)}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">当前经销商:</span>
                      <span>{order.dealerName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">订单金额:</span>
                      <span className="font-medium">¥{order.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">创建时间:</span>
                      <span>{new Date(order.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="mt-4 flex justify-end">
                    <Button
                      onClick={() => openSwitchDialog(order)}
                      disabled={!order.canSwitch}
                      size="sm"
                    >
                      {order.canSwitch ? '切换经销商' : '不可切换'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}

            {orders.length === 0 && (
              <div className="text-center py-8">
                <div className="text-gray-500">暂无订单数据</div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 切换经销商对话框 */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>切换订单经销商</DialogTitle>
          </DialogHeader>
          
          {selectedOrder && (
            <div className="space-y-4">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">订单信息</div>
                <div className="font-medium">{selectedOrder.orderNumber}</div>
                <div className="text-sm">客户: {selectedOrder.customerName}</div>
                <div className="text-sm">当前经销商: {selectedOrder.dealerName}</div>
              </div>

              <div>
                <label className="text-sm font-medium">选择新经销商</label>
                <Select value={selectedDealer} onValueChange={setSelectedDealer}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="请选择经销商" />
                  </SelectTrigger>
                  <SelectContent>
                    {dealers
                      .filter(dealer => dealer.id !== selectedOrder.dealerId)
                      .map((dealer) => (
                        <SelectItem key={dealer.id} value={dealer.id}>
                          {dealer.name} ({dealer.code})
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  取消
                </Button>
                <Button 
                  onClick={handleSwitchOrder}
                  disabled={!selectedDealer || switching}
                >
                  {switching ? '切换中...' : '确认切换'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="grid grid-cols-4">
          <Link href="/" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/tasks" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">✓</div>
            <span className="text-xs">任务</span>
          </Link>
          <Link href="/order-switch" className="flex flex-col items-center py-3 text-blue-600">
            <div className="w-6 h-6 mb-1">🔄</div>
            <span className="text-xs">订单切换</span>
          </Link>
          <Link href="/quota" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">📊</div>
            <span className="text-xs">配额</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
