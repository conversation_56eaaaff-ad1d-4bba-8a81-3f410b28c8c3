{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.tsx"], "sourcesContent": ["const NOOP = (fn: () => void) => fn()\n\n/**\n * Returns a transition function that can be used to wrap router actions.\n * This allows us to tap into the transition state of the router as an\n * approximation of React render time.\n */\nexport const useSyncDevRenderIndicator = () => {\n  let syncDevRenderIndicator = NOOP\n\n  if (process.env.NODE_ENV === 'development') {\n    const { useSyncDevRenderIndicatorInternal } =\n      require('./use-sync-dev-render-indicator-internal') as typeof import('./use-sync-dev-render-indicator-internal')\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    syncDevRenderIndicator = useSyncDevRenderIndicatorInternal()\n  }\n\n  return syncDevRenderIndicator\n}\n"], "names": ["NOOP", "fn", "useSyncDevRenderIndicator", "syncDevRenderIndicator", "process", "env", "NODE_ENV", "useSyncDevRenderIndicatorInternal", "require"], "mappings": "AAAA,MAAMA,OAAO,CAACC,KAAmBA;AAEjC;;;;CAIC,GACD,OAAO,MAAMC,4BAA4B;IACvC,IAAIC,yBAAyBH;IAE7B,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,iCAAiC,EAAE,GACzCC,QAAQ;QAEV,sDAAsD;QACtDL,yBAAyBI;IAC3B;IAEA,OAAOJ;AACT,EAAC"}