"use client"

import { useState, useEffect } from 'react'
import { ArrowLeft, Search, Plus, Edit, Check, X, History } from 'lucide-react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { Quota, Dealer } from '@/lib/types'
import { quotaApi, dealerApi } from '@/lib/api'

export default function QuotaPage() {
  const [quotas, setQuotas] = useState<Quota[]>([])
  const [dealers, setDealers] = useState<Dealer[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDealer, setSelectedDealer] = useState<string>('')
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingQuota, setEditingQuota] = useState<Quota | null>(null)
  const [newAmount, setNewAmount] = useState('')
  const { toast } = useToast()

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      draft: '草稿',
      pending: '待确认',
      confirmed: '已确认',
      adjusted: '已调整'
    }
    return statusMap[status] || status
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      draft: 'bg-gray-100 text-gray-800',
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-green-100 text-green-800',
      adjusted: 'bg-blue-100 text-blue-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  // 计算使用率
  const getUsagePercentage = (used: number, allocated: number) => {
    return allocated > 0 ? Math.round((used / allocated) * 100) : 0
  }

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const [quotasResponse, dealersResponse] = await Promise.all([
        quotaApi.getQuotas({ dealerId: selectedDealer }),
        dealerApi.getDealers({ keyword: '' })
      ])

      if (quotasResponse.success && quotasResponse.data) {
        setQuotas(quotasResponse.data)
      }

      if (dealersResponse.success && dealersResponse.data) {
        setDealers(dealersResponse.data)
      }
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载配额数据，请稍后重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 打开编辑对话框
  const openEditDialog = (quota: Quota) => {
    setEditingQuota(quota)
    setNewAmount(quota.allocatedAmount.toString())
    setDialogOpen(true)
  }

  // 处理配额调整
  const handleAdjustQuota = async () => {
    if (!editingQuota || !newAmount) return

    const amount = parseFloat(newAmount)
    if (isNaN(amount) || amount < 0) {
      toast({
        title: '输入错误',
        description: '请输入有效的配额金额',
        variant: 'destructive'
      })
      return
    }

    // 模拟API调用
    try {
      // 这里应该调用真实的API
      const updatedQuota = {
        ...editingQuota,
        allocatedAmount: amount,
        remainingAmount: amount - editingQuota.usedAmount,
        status: 'adjusted' as const,
        updatedAt: new Date().toISOString()
      }

      // 更新本地状态
      setQuotas(prev => prev.map(q => q.id === editingQuota.id ? updatedQuota : q))

      toast({
        title: '调整成功',
        description: '配额已成功调整'
      })

      setDialogOpen(false)
      setEditingQuota(null)
      setNewAmount('')
    } catch (error) {
      toast({
        title: '调整失败',
        description: '配额调整失败，请稍后重试',
        variant: 'destructive'
      })
    }
  }

  // 确认配额
  const handleConfirmQuota = async (quotaId: string) => {
    try {
      // 模拟API调用
      setQuotas(prev => prev.map(q =>
        q.id === quotaId
          ? { ...q, status: 'confirmed' as const, updatedAt: new Date().toISOString() }
          : q
      ))

      toast({
        title: '确认成功',
        description: '配额已确认'
      })
    } catch (error) {
      toast({
        title: '确认失败',
        description: '配额确认失败，请稍后重试',
        variant: 'destructive'
      })
    }
  }

  useEffect(() => {
    loadData()
  }, [selectedDealer])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center">
        <Link href="/">
          <ArrowLeft className="w-6 h-6 mr-4" />
        </Link>
        <h1 className="text-xl font-semibold">配额管理</h1>
      </div>

      <div className="p-4 space-y-4">
        {/* 筛选器 */}
        <div className="flex gap-2">
          <Select value={selectedDealer} onValueChange={setSelectedDealer}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="选择经销商" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部经销商</SelectItem>
              {dealers.map((dealer) => (
                <SelectItem key={dealer.id} value={dealer.id}>
                  {dealer.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Plus className="w-4 h-4" />
          </Button>
        </div>

        {/* 配额列表 */}
        {loading ? (
          <div className="text-center py-8">
            <div className="text-gray-500">加载中...</div>
          </div>
        ) : (
          <div className="space-y-4">
            {quotas.map((quota) => (
              <Card key={quota.id}>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{quota.dealerName}</CardTitle>
                      <p className="text-sm text-gray-600">{quota.productCategory} - {quota.period}</p>
                    </div>
                    <Badge className={getStatusColor(quota.status)}>
                      {getStatusText(quota.status)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 配额使用情况 */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>使用进度</span>
                      <span>{getUsagePercentage(quota.usedAmount, quota.allocatedAmount)}%</span>
                    </div>
                    <Progress
                      value={getUsagePercentage(quota.usedAmount, quota.allocatedAmount)}
                      className="h-2"
                    />
                  </div>

                  {/* 配额详情 */}
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-gray-600">总配额</div>
                      <div className="font-semibold">¥{quota.allocatedAmount.toLocaleString()}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-gray-600">已使用</div>
                      <div className="font-semibold text-blue-600">¥{quota.usedAmount.toLocaleString()}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-gray-600">剩余</div>
                      <div className="font-semibold text-green-600">¥{quota.remainingAmount.toLocaleString()}</div>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(quota)}
                      className="flex-1"
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      调整
                    </Button>
                    {quota.status !== 'confirmed' && (
                      <Button
                        size="sm"
                        onClick={() => handleConfirmQuota(quota.id)}
                        className="flex-1"
                      >
                        <Check className="w-4 h-4 mr-1" />
                        确认
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <History className="w-4 h-4 mr-1" />
                      历史
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}

            {quotas.length === 0 && (
              <div className="text-center py-8">
                <div className="text-gray-500">暂无配额数据</div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 配额调整对话框 */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>调整配额</DialogTitle>
          </DialogHeader>

          {editingQuota && (
            <div className="space-y-4">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">经销商</div>
                <div className="font-medium">{editingQuota.dealerName}</div>
                <div className="text-sm">{editingQuota.productCategory} - {editingQuota.period}</div>
              </div>

              <div className="space-y-2">
                <div className="text-sm text-gray-600">当前配额: ¥{editingQuota.allocatedAmount.toLocaleString()}</div>
                <div className="text-sm text-gray-600">已使用: ¥{editingQuota.usedAmount.toLocaleString()}</div>
              </div>

              <div>
                <label className="text-sm font-medium">新配额金额</label>
                <Input
                  type="number"
                  value={newAmount}
                  onChange={(e) => setNewAmount(e.target.value)}
                  placeholder="请输入新的配额金额"
                  className="mt-1"
                />
              </div>

              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAdjustQuota}>
                  确认调整
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="grid grid-cols-4">
          <Link href="/" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/tasks" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">✓</div>
            <span className="text-xs">任务</span>
          </Link>
          <Link href="/order-switch" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🔄</div>
            <span className="text-xs">订单切换</span>
          </Link>
          <Link href="/quota" className="flex flex-col items-center py-3 text-blue-600">
            <div className="w-6 h-6 mb-1">📊</div>
            <span className="text-xs">配额</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
