{"version": 3, "sources": ["../../../src/server/dev/turbopack-utils.ts"], "sourcesContent": ["import type {\n  ServerFields,\n  SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport type {\n  Issue,\n  TurbopackResult,\n  Endpoint,\n  RawEntrypoints,\n  Update as TurbopackUpdate,\n  WrittenEndpoint,\n} from '../../build/swc/types'\nimport {\n  type HMR_ACTION_TYPES,\n  HMR_ACTIONS_SENT_TO_BROWSER,\n} from './hot-reloader-types'\nimport * as Log from '../../build/output/log'\nimport type { PropagateToWorkersField } from '../lib/router-utils/types'\nimport type { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport type { AppRoute, Entrypoints, PageRoute } from '../../build/swc/types'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from '../../shared/lib/turbopack/entry-key'\nimport type ws from 'next/dist/compiled/ws'\nimport { isMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\nimport {\n  formatIssue,\n  getIssueKey,\n  isRelevantWarning,\n  processIssues,\n  renderStyledStringToErrorAnsi,\n  type EntryIssuesMap,\n  type TopLevelIssuesMap,\n} from '../../shared/lib/turbopack/utils'\n\nconst onceErrorSet = new Set()\n/**\n * Check if given issue is a warning to be display only once.\n * This mimics behavior of get-page-static-info's warnOnce.\n * @param issue\n * @returns\n */\nfunction shouldEmitOnceWarning(issue: Issue): boolean {\n  const { severity, title, stage } = issue\n  if (severity === 'warning' && title.value === 'Invalid page configuration') {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n  if (\n    severity === 'warning' &&\n    stage === 'config' &&\n    renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n  ) {\n    if (onceErrorSet.has(issue)) {\n      return false\n    }\n    onceErrorSet.add(issue)\n  }\n\n  return true\n}\n\n/// Print out an issue to the console which should not block\n/// the build by throwing out or blocking error overlay.\nexport function printNonFatalIssue(issue: Issue) {\n  if (isRelevantWarning(issue) && shouldEmitOnceWarning(issue)) {\n    Log.warn(formatIssue(issue))\n  }\n}\n\nexport function processTopLevelIssues(\n  currentTopLevelIssues: TopLevelIssuesMap,\n  result: TurbopackResult\n) {\n  currentTopLevelIssues.clear()\n\n  for (const issue of result.issues) {\n    const issueKey = getIssueKey(issue)\n    currentTopLevelIssues.set(issueKey, issue)\n  }\n}\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nexport function msToNs(ms: number): bigint {\n  return BigInt(Math.floor(ms)) * MILLISECONDS_IN_NANOSECOND\n}\n\nexport type ChangeSubscriptions = Map<\n  EntryKey,\n  Promise<AsyncIterableIterator<TurbopackResult>>\n>\n\nexport type HandleWrittenEndpoint = (\n  key: EntryKey,\n  result: TurbopackResult<WrittenEndpoint>\n) => void\n\nexport type StartChangeSubscription = (\n  key: EntryKey,\n  includeIssues: boolean,\n  endpoint: Endpoint,\n  makePayload: (\n    change: TurbopackResult,\n    hash: string\n  ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n  onError?: (e: Error) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n) => Promise<void>\n\nexport type StopChangeSubscription = (key: EntryKey) => Promise<void>\n\nexport type SendHmr = (id: string, payload: HMR_ACTION_TYPES) => void\n\nexport type StartBuilding = (\n  id: string,\n  requestUrl: string | undefined,\n  forceRebuild: boolean\n) => () => void\n\nexport type ReadyIds = Set<string>\n\nexport type ClientState = {\n  clientIssues: EntryIssuesMap\n  hmrPayloads: Map<string, HMR_ACTION_TYPES>\n  turbopackUpdates: TurbopackUpdate[]\n  subscriptions: Map<string, AsyncIterator<any>>\n}\n\nexport type ClientStateMap = WeakMap<ws, ClientState>\n\n// hooks only used by the dev server.\ntype HandleRouteTypeHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  subscribeToChanges: StartChangeSubscription\n}\n\nexport async function handleRouteType({\n  dev,\n  page,\n  pathname,\n  route,\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  readyIds,\n  devRewrites,\n  productionRewrites,\n  hooks,\n  logErrors,\n}: {\n  dev: boolean\n  page: string\n  pathname: string\n  route: PageRoute | AppRoute\n\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  readyIds?: ReadyIds // dev\n\n  hooks?: HandleRouteTypeHooks // dev\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const clientKey = getEntryKey('pages', 'client', page)\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      try {\n        if (entrypoints.global.app) {\n          const key = getEntryKey('pages', 'server', '_app')\n\n          const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n          hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadBuildManifest('_app')\n        await manifestLoader.loadPagesManifest('_app')\n\n        if (entrypoints.global.document) {\n          const key = getEntryKey('pages', 'server', '_document')\n\n          const writtenEndpoint =\n            await entrypoints.global.document.writeToDisk()\n          hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n          processIssues(\n            currentEntryIssues,\n            key,\n            writtenEndpoint,\n            false,\n            logErrors\n          )\n        }\n        await manifestLoader.loadPagesManifest('_document')\n\n        const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n        hooks?.handleWrittenEndpoint(serverKey, writtenEndpoint)\n\n        const type = writtenEndpoint?.type\n\n        await manifestLoader.loadBuildManifest(page)\n        await manifestLoader.loadPagesManifest(page)\n        if (type === 'edge') {\n          await manifestLoader.loadMiddlewareManifest(page, 'pages')\n        } else {\n          manifestLoader.deleteMiddlewareManifest(serverKey)\n        }\n        await manifestLoader.loadFontManifest('/_app', 'pages')\n        await manifestLoader.loadFontManifest(page, 'pages')\n\n        if (shouldCreateWebpackStats) {\n          await manifestLoader.loadWebpackStats(page, 'pages')\n        }\n\n        await manifestLoader.writeManifests({\n          devRewrites,\n          productionRewrites,\n          entrypoints,\n        })\n\n        processIssues(\n          currentEntryIssues,\n          serverKey,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      } finally {\n        if (dev) {\n          // TODO subscriptions should only be caused by the WebSocket connections\n          // otherwise we don't known when to unsubscribe and this leaking\n          hooks?.subscribeToChanges(\n            serverKey,\n            false,\n            route.dataEndpoint,\n            () => {\n              // Report the next compilation again\n              readyIds?.delete(pathname)\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n                pages: [page],\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} data subscription: ${e}`,\n              }\n            }\n          )\n          hooks?.subscribeToChanges(\n            clientKey,\n            false,\n            route.htmlEndpoint,\n            () => {\n              return {\n                event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES,\n              }\n            },\n            (e) => {\n              return {\n                action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                data: `error in ${page} html subscription: ${e}`,\n              }\n            }\n          )\n          if (entrypoints.global.document) {\n            hooks?.subscribeToChanges(\n              getEntryKey('pages', 'server', '_document'),\n              false,\n              entrypoints.global.document,\n              () => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: '_document has changed (page route)',\n                }\n              },\n              (e) => {\n                return {\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n                  data: `error in _document subscription (page route): ${e}`,\n                }\n              }\n            )\n          }\n        }\n      }\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      if (dev) {\n        // TODO subscriptions should only be caused by the WebSocket connections\n        // otherwise we don't known when to unsubscribe and this leaking\n        hooks?.subscribeToChanges(\n          key,\n          true,\n          route.rscEndpoint,\n          (change, hash) => {\n            if (change.issues.some((issue) => issue.severity === 'error')) {\n              // Ignore any updates that has errors\n              // There will be another update without errors eventually\n              return\n            }\n            // Report the next compilation again\n            readyIds?.delete(pathname)\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n              hash,\n            }\n          },\n          (e) => {\n            return {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n              data: `error in ${page} app-page subscription: ${e}`,\n            }\n          }\n        )\n      }\n\n      const type = writtenEndpoint.type\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, dev, logErrors)\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      hooks?.handleWrittenEndpoint(key, writtenEndpoint)\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites,\n        productionRewrites,\n        entrypoints,\n      })\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n\n/**\n * Maintains a mapping between entrypoins and the corresponding client asset paths.\n */\nexport class AssetMapper {\n  private entryMap: Map<EntryKey, Set<string>> = new Map()\n  private assetMap: Map<string, Set<EntryKey>> = new Map()\n\n  /**\n   * Overrides asset paths for a key and updates the mapping from path to key.\n   *\n   * @param key\n   * @param assetPaths asset paths relative to the .next directory\n   */\n  setPathsForKey(key: EntryKey, assetPaths: string[]): void {\n    this.delete(key)\n\n    const newAssetPaths = new Set(assetPaths)\n    this.entryMap.set(key, newAssetPaths)\n\n    for (const assetPath of newAssetPaths) {\n      let assetPathKeys = this.assetMap.get(assetPath)\n      if (!assetPathKeys) {\n        assetPathKeys = new Set()\n        this.assetMap.set(assetPath, assetPathKeys)\n      }\n\n      assetPathKeys!.add(key)\n    }\n  }\n\n  /**\n   * Deletes the key and any asset only referenced by this key.\n   *\n   * @param key\n   */\n  delete(key: EntryKey) {\n    for (const assetPath of this.getAssetPathsByKey(key)) {\n      const assetPathKeys = this.assetMap.get(assetPath)\n\n      assetPathKeys?.delete(key)\n\n      if (!assetPathKeys?.size) {\n        this.assetMap.delete(assetPath)\n      }\n    }\n\n    this.entryMap.delete(key)\n  }\n\n  getAssetPathsByKey(key: EntryKey): string[] {\n    return Array.from(this.entryMap.get(key) ?? [])\n  }\n\n  getKeysByAsset(path: string): EntryKey[] {\n    return Array.from(this.assetMap.get(path) ?? [])\n  }\n\n  keys(): IterableIterator<EntryKey> {\n    return this.entryMap.keys()\n  }\n}\n\nexport function hasEntrypointForKey(\n  entrypoints: Entrypoints,\n  key: EntryKey,\n  assetMapper: AssetMapper | undefined\n): boolean {\n  const { type, page } = splitEntryKey(key)\n\n  switch (type) {\n    case 'app':\n      return entrypoints.app.has(page)\n    case 'pages':\n      switch (page) {\n        case '_app':\n          return entrypoints.global.app != null\n        case '_document':\n          return entrypoints.global.document != null\n        case '_error':\n          return entrypoints.global.error != null\n        default:\n          return entrypoints.page.has(page)\n      }\n    case 'root':\n      switch (page) {\n        case 'middleware':\n          return entrypoints.global.middleware != null\n        case 'instrumentation':\n          return entrypoints.global.instrumentation != null\n        default:\n          return false\n      }\n    case 'assets':\n      if (!assetMapper) {\n        return false\n      }\n\n      return assetMapper\n        .getKeysByAsset(page)\n        .some((pageKey) =>\n          hasEntrypointForKey(entrypoints, pageKey, assetMapper)\n        )\n    default: {\n      // validation that we covered all cases, this should never run.\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = type\n      return false\n    }\n  }\n}\n\n// hooks only used by the dev server.\ntype HandleEntrypointsHooks = {\n  handleWrittenEndpoint: HandleWrittenEndpoint\n  propagateServerField: (\n    field: PropagateToWorkersField,\n    args: any\n  ) => Promise<void>\n  sendHmr: SendHmr\n  startBuilding: StartBuilding\n  subscribeToChanges: StartChangeSubscription\n  unsubscribeFromChanges: StopChangeSubscription\n  unsubscribeFromHmrEvents: (client: ws, id: string) => void\n}\n\ntype HandleEntrypointsDevOpts = {\n  assetMapper: AssetMapper\n  changeSubscriptions: ChangeSubscriptions\n  clients: Set<ws>\n  clientStates: ClientStateMap\n  serverFields: ServerFields\n\n  hooks: HandleEntrypointsHooks\n}\n\nexport async function handleEntrypoints({\n  entrypoints,\n\n  currentEntrypoints,\n\n  currentEntryIssues,\n  manifestLoader,\n  devRewrites,\n  logErrors,\n  dev,\n}: {\n  entrypoints: TurbopackResult<RawEntrypoints>\n\n  currentEntrypoints: Entrypoints\n\n  currentEntryIssues: EntryIssuesMap\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n\n  dev: HandleEntrypointsDevOpts\n}) {\n  currentEntrypoints.global.app = entrypoints.pagesAppEndpoint\n  currentEntrypoints.global.document = entrypoints.pagesDocumentEndpoint\n  currentEntrypoints.global.error = entrypoints.pagesErrorEndpoint\n\n  currentEntrypoints.global.instrumentation = entrypoints.instrumentation\n\n  currentEntrypoints.page.clear()\n  currentEntrypoints.app.clear()\n\n  for (const [pathname, route] of entrypoints.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        currentEntrypoints.page.set(pathname, route)\n        break\n      case 'app-page': {\n        route.pages.forEach((page) => {\n          currentEntrypoints.app.set(page.originalName, {\n            type: 'app-page',\n            ...page,\n          })\n        })\n        break\n      }\n      case 'app-route': {\n        currentEntrypoints.app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  if (dev) {\n    await handleEntrypointsDevCleanup({\n      currentEntryIssues,\n      currentEntrypoints,\n\n      ...dev,\n    })\n  }\n\n  const { middleware, instrumentation } = entrypoints\n\n  // We check for explicit true/false, since it's initialized to\n  // undefined during the first loop (middlewareChanges event is\n  // unnecessary during the first serve)\n  if (currentEntrypoints.global.middleware && !middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n    // Went from middleware to no middleware\n    await dev?.hooks.unsubscribeFromChanges(key)\n    currentEntryIssues.delete(key)\n    dev.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  } else if (!currentEntrypoints.global.middleware && middleware) {\n    // Went from no middleware to middleware\n    dev.hooks.sendHmr('middleware', {\n      event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n    })\n  }\n\n  currentEntrypoints.global.middleware = middleware\n\n  if (instrumentation) {\n    const processInstrumentation = async (\n      name: string,\n      prop: 'nodeJs' | 'edge'\n    ) => {\n      const key = getEntryKey('root', 'server', name)\n\n      const writtenEndpoint = await instrumentation[prop].writeToDisk()\n      dev.hooks.handleWrittenEndpoint(key, writtenEndpoint)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n    }\n    await processInstrumentation('instrumentation.nodeJs', 'nodeJs')\n    await processInstrumentation('instrumentation.edge', 'edge')\n    await manifestLoader.loadMiddlewareManifest(\n      'instrumentation',\n      'instrumentation'\n    )\n    await manifestLoader.writeManifests({\n      devRewrites,\n      productionRewrites: undefined,\n      entrypoints: currentEntrypoints,\n    })\n\n    dev.serverFields.actualInstrumentationHookFile = '/instrumentation'\n    await dev.hooks.propagateServerField(\n      'actualInstrumentationHookFile',\n      dev.serverFields.actualInstrumentationHookFile\n    )\n  } else {\n    dev.serverFields.actualInstrumentationHookFile = undefined\n    await dev.hooks.propagateServerField(\n      'actualInstrumentationHookFile',\n      dev.serverFields.actualInstrumentationHookFile\n    )\n  }\n\n  if (middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n\n    const endpoint = middleware.endpoint\n\n    async function processMiddleware() {\n      const writtenEndpoint = await endpoint.writeToDisk()\n      dev.hooks.handleWrittenEndpoint(key, writtenEndpoint)\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n      await manifestLoader.loadMiddlewareManifest('middleware', 'middleware')\n      const middlewareConfig =\n        manifestLoader.getMiddlewareManifest(key)?.middleware['/']\n\n      if (dev && middlewareConfig) {\n        dev.serverFields.middleware = {\n          match: null as any,\n          page: '/',\n          matchers: middlewareConfig.matchers,\n        }\n      }\n    }\n    await processMiddleware()\n\n    if (dev) {\n      dev?.hooks.subscribeToChanges(\n        key,\n        false,\n        endpoint,\n        async () => {\n          const finishBuilding = dev.hooks.startBuilding(\n            'middleware',\n            undefined,\n            true\n          )\n          await processMiddleware()\n          await dev.hooks.propagateServerField(\n            'actualMiddlewareFile',\n            dev.serverFields.actualMiddlewareFile\n          )\n          await dev.hooks.propagateServerField(\n            'middleware',\n            dev.serverFields.middleware\n          )\n          await manifestLoader.writeManifests({\n            devRewrites,\n            productionRewrites: undefined,\n            entrypoints: currentEntrypoints,\n          })\n\n          finishBuilding?.()\n          return { event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES }\n        },\n        () => {\n          return {\n            event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n          }\n        }\n      )\n    }\n  } else {\n    manifestLoader.deleteMiddlewareManifest(\n      getEntryKey('root', 'server', 'middleware')\n    )\n    dev.serverFields.actualMiddlewareFile = undefined\n    dev.serverFields.middleware = undefined\n  }\n\n  await dev.hooks.propagateServerField(\n    'actualMiddlewareFile',\n    dev.serverFields.actualMiddlewareFile\n  )\n  await dev.hooks.propagateServerField(\n    'middleware',\n    dev.serverFields.middleware\n  )\n}\n\nasync function handleEntrypointsDevCleanup({\n  currentEntryIssues,\n  currentEntrypoints,\n\n  assetMapper,\n  changeSubscriptions,\n  clients,\n  clientStates,\n\n  hooks,\n}: {\n  currentEntrypoints: Entrypoints\n  currentEntryIssues: EntryIssuesMap\n} & HandleEntrypointsDevOpts) {\n  // this needs to be first as `hasEntrypointForKey` uses the `assetMapper`\n  for (const key of assetMapper.keys()) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      assetMapper.delete(key)\n    }\n  }\n\n  for (const key of changeSubscriptions.keys()) {\n    // middleware is handled separately\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      await hooks.unsubscribeFromChanges(key)\n    }\n  }\n\n  for (const [key] of currentEntryIssues) {\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      currentEntryIssues.delete(key)\n    }\n  }\n\n  for (const client of clients) {\n    const state = clientStates.get(client)\n    if (!state) {\n      continue\n    }\n\n    for (const key of state.clientIssues.keys()) {\n      if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n        state.clientIssues.delete(key)\n      }\n    }\n\n    for (const id of state.subscriptions.keys()) {\n      if (\n        !hasEntrypointForKey(\n          currentEntrypoints,\n          getEntryKey('assets', 'client', id),\n          assetMapper\n        )\n      ) {\n        hooks.unsubscribeFromHmrEvents(client, id)\n      }\n    }\n  }\n}\n\nexport async function handlePagesErrorRoute({\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  devRewrites,\n  productionRewrites,\n  logErrors,\n  hooks,\n}: {\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n  hooks: HandleRouteTypeHooks\n}) {\n  if (entrypoints.global.app) {\n    const key = getEntryKey('pages', 'server', '_app')\n\n    const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.app,\n      () => {\n        // There's a special case for this in `../client/page-bootstrap.ts`.\n        // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n        return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n      },\n      () => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_app has changed (error route)',\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_app')\n  await manifestLoader.loadPagesManifest('_app')\n  await manifestLoader.loadFontManifest('_app')\n\n  if (entrypoints.global.document) {\n    const key = getEntryKey('pages', 'server', '_document')\n\n    const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.document,\n      () => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_document has changed (error route)',\n        }\n      },\n      (e) => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: `error in _document subscription (error route): ${e}`,\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadPagesManifest('_document')\n\n  if (entrypoints.global.error) {\n    const key = getEntryKey('pages', 'server', '_error')\n\n    const writtenEndpoint = await entrypoints.global.error.writeToDisk()\n    hooks.handleWrittenEndpoint(key, writtenEndpoint)\n    hooks.subscribeToChanges(\n      key,\n      false,\n      entrypoints.global.error,\n      () => {\n        // There's a special case for this in `../client/page-bootstrap.ts`.\n        // https://github.com/vercel/next.js/blob/08d7a7e5189a835f5dcb82af026174e587575c0e/packages/next/src/client/page-bootstrap.ts#L69-L71\n        return { event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES }\n      },\n      (e) => {\n        return {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: `error in _error subscription: ${e}`,\n        }\n      }\n    )\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_error')\n  await manifestLoader.loadPagesManifest('_error')\n  await manifestLoader.loadFontManifest('_error')\n\n  await manifestLoader.writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  })\n}\n\nexport function removeRouteSuffix(route: string): string {\n  return route.replace(/\\/route$/, '')\n}\n\nexport function addRouteSuffix(route: string): string {\n  return route + '/route'\n}\n\nexport function addMetadataIdToRoute(route: string): string {\n  return route + '/[__metadata_id__]'\n}\n\n// Since turbopack will create app pages/route entries based on the structure,\n// which means the entry keys are based on file names.\n// But for special metadata conventions we'll change the page/pathname to a different path.\n// So we need this helper to map the new path back to original turbopack entry key.\nexport function normalizedPageToTurbopackStructureRoute(\n  route: string,\n  ext: string | false\n): string {\n  let entrypointKey = route\n  if (isMetadataRoute(entrypointKey)) {\n    entrypointKey = entrypointKey.endsWith('/route')\n      ? entrypointKey.slice(0, -'/route'.length)\n      : entrypointKey\n\n    if (ext) {\n      if (entrypointKey.endsWith('/[__metadata_id__]')) {\n        entrypointKey = entrypointKey.slice(0, -'/[__metadata_id__]'.length)\n      }\n      if (entrypointKey.endsWith('/sitemap.xml') && ext !== '.xml') {\n        // For dynamic sitemap route, remove the extension\n        entrypointKey = entrypointKey.slice(0, -'.xml'.length)\n      }\n    }\n    entrypointKey = entrypointKey + '/route'\n  }\n  return entrypointKey\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER", "Log", "getEntry<PERSON>ey", "splitEntryKey", "isMetadataRoute", "formatIssue", "getIssueKey", "isRelevantWarning", "processIssues", "renderStyledStringToErrorAnsi", "onceErrorSet", "Set", "shouldEmitOnceWarning", "issue", "severity", "title", "stage", "value", "has", "add", "includes", "printNonFatalIssue", "warn", "processTopLevelIssues", "currentTopLevelIssues", "result", "clear", "issues", "issue<PERSON><PERSON>", "set", "MILLISECONDS_IN_NANOSECOND", "BigInt", "msToNs", "ms", "Math", "floor", "handleRouteType", "dev", "page", "pathname", "route", "currentEntryIssues", "entrypoints", "manifest<PERSON><PERSON>der", "readyIds", "devRewrites", "productionRewrites", "hooks", "logErrors", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "type", "client<PERSON>ey", "server<PERSON>ey", "global", "app", "key", "writtenEndpoint", "writeToDisk", "handleWrittenEndpoint", "loadBuildManifest", "loadPagesManifest", "document", "htmlEndpoint", "loadMiddlewareManifest", "deleteMiddlewareManifest", "loadFontManifest", "loadWebpackStats", "writeManifests", "subscribeToChanges", "dataEndpoint", "delete", "event", "SERVER_ONLY_CHANGES", "pages", "e", "action", "RELOAD_PAGE", "data", "CLIENT_CHANGES", "endpoint", "rscEndpoint", "change", "hash", "some", "SERVER_COMPONENT_CHANGES", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error", "AssetMapper", "setPathsFor<PERSON>ey", "assetPaths", "newAssetPaths", "entryMap", "assetPath", "assetPathKeys", "assetMap", "get", "getAssetPathsByKey", "size", "Array", "from", "getKeysByAsset", "path", "keys", "Map", "hasEntrypointForKey", "assetMapper", "error", "middleware", "instrumentation", "page<PERSON><PERSON>", "_", "handleEntrypoints", "currentEntrypoints", "pagesAppEndpoint", "pagesDocumentEndpoint", "pagesErrorEndpoint", "routes", "for<PERSON>ach", "originalName", "info", "handleEntrypointsDevCleanup", "unsubscribeFromChanges", "sendHmr", "MIDDLEWARE_CHANGES", "processInstrumentation", "name", "prop", "undefined", "serverFields", "actualInstrumentationHookFile", "propagateServerField", "processMiddleware", "middlewareConfig", "getMiddlewareManifest", "match", "matchers", "finishBuilding", "startBuilding", "actualMiddlewareFile", "changeSubscriptions", "clients", "clientStates", "client", "state", "clientIssues", "id", "subscriptions", "unsubscribeFromHmrEvents", "handlePagesErrorRoute", "removeRouteSuffix", "replace", "addRouteSuffix", "addMetadataIdToRoute", "normalizedPageToTurbopackStructureRoute", "ext", "entrypoint<PERSON><PERSON>", "endsWith", "slice", "length"], "mappings": "AAYA,SAEEA,2BAA2B,QACtB,uBAAsB;AAC7B,YAAYC,SAAS,yBAAwB;AAI7C,SAEEC,WAAW,EACXC,aAAa,QACR,uCAAsC;AAE7C,SAASC,eAAe,QAAQ,uCAAsC;AAEtE,SACEC,WAAW,EACXC,WAAW,EACXC,iBAAiB,EACjBC,aAAa,EACbC,6BAA6B,QAGxB,mCAAkC;AAEzC,MAAMC,eAAe,IAAIC;AACzB;;;;;CAKC,GACD,SAASC,sBAAsBC,KAAY;IACzC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,EAAE,GAAGH;IACnC,IAAIC,aAAa,aAAaC,MAAME,KAAK,KAAK,8BAA8B;QAC1E,IAAIP,aAAaQ,GAAG,CAACL,QAAQ;YAC3B,OAAO;QACT;QACAH,aAAaS,GAAG,CAACN;IACnB;IACA,IACEC,aAAa,aACbE,UAAU,YACVP,8BAA8BI,MAAME,KAAK,EAAEK,QAAQ,CAAC,sBACpD;QACA,IAAIV,aAAaQ,GAAG,CAACL,QAAQ;YAC3B,OAAO;QACT;QACAH,aAAaS,GAAG,CAACN;IACnB;IAEA,OAAO;AACT;AAEA,4DAA4D;AAC5D,wDAAwD;AACxD,OAAO,SAASQ,mBAAmBR,KAAY;IAC7C,IAAIN,kBAAkBM,UAAUD,sBAAsBC,QAAQ;QAC5DZ,IAAIqB,IAAI,CAACjB,YAAYQ;IACvB;AACF;AAEA,OAAO,SAASU,sBACdC,qBAAwC,EACxCC,MAAuB;IAEvBD,sBAAsBE,KAAK;IAE3B,KAAK,MAAMb,SAASY,OAAOE,MAAM,CAAE;QACjC,MAAMC,WAAWtB,YAAYO;QAC7BW,sBAAsBK,GAAG,CAACD,UAAUf;IACtC;AACF;AAEA,MAAMiB,6BAA6BC,OAAO;AAE1C,OAAO,SAASC,OAAOC,EAAU;IAC/B,OAAOF,OAAOG,KAAKC,KAAK,CAACF,OAAOH;AAClC;AAkDA,OAAO,eAAeM,gBAAgB,EACpCC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EAClBC,KAAK,EACLC,SAAS,EAiBV;IACC,MAAMC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQZ,MAAMa,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAMC,YAAYpD,YAAY,SAAS,UAAUoC;gBACjD,MAAMiB,YAAYrD,YAAY,SAAS,UAAUoC;gBAEjD,IAAI;oBACF,IAAII,YAAYc,MAAM,CAACC,GAAG,EAAE;wBAC1B,MAAMC,MAAMxD,YAAY,SAAS,UAAU;wBAE3C,MAAMyD,kBAAkB,MAAMjB,YAAYc,MAAM,CAACC,GAAG,CAACG,WAAW;wBAChEb,yBAAAA,MAAOc,qBAAqB,CAACH,KAAKC;wBAClCnD,cACEiC,oBACAiB,KACAC,iBACA,OACAX;oBAEJ;oBACA,MAAML,eAAemB,iBAAiB,CAAC;oBACvC,MAAMnB,eAAeoB,iBAAiB,CAAC;oBAEvC,IAAIrB,YAAYc,MAAM,CAACQ,QAAQ,EAAE;wBAC/B,MAAMN,MAAMxD,YAAY,SAAS,UAAU;wBAE3C,MAAMyD,kBACJ,MAAMjB,YAAYc,MAAM,CAACQ,QAAQ,CAACJ,WAAW;wBAC/Cb,yBAAAA,MAAOc,qBAAqB,CAACH,KAAKC;wBAClCnD,cACEiC,oBACAiB,KACAC,iBACA,OACAX;oBAEJ;oBACA,MAAML,eAAeoB,iBAAiB,CAAC;oBAEvC,MAAMJ,kBAAkB,MAAMnB,MAAMyB,YAAY,CAACL,WAAW;oBAC5Db,yBAAAA,MAAOc,qBAAqB,CAACN,WAAWI;oBAExC,MAAMN,OAAOM,mCAAAA,gBAAiBN,IAAI;oBAElC,MAAMV,eAAemB,iBAAiB,CAACxB;oBACvC,MAAMK,eAAeoB,iBAAiB,CAACzB;oBACvC,IAAIe,SAAS,QAAQ;wBACnB,MAAMV,eAAeuB,sBAAsB,CAAC5B,MAAM;oBACpD,OAAO;wBACLK,eAAewB,wBAAwB,CAACZ;oBAC1C;oBACA,MAAMZ,eAAeyB,gBAAgB,CAAC,SAAS;oBAC/C,MAAMzB,eAAeyB,gBAAgB,CAAC9B,MAAM;oBAE5C,IAAIW,0BAA0B;wBAC5B,MAAMN,eAAe0B,gBAAgB,CAAC/B,MAAM;oBAC9C;oBAEA,MAAMK,eAAe2B,cAAc,CAAC;wBAClCzB;wBACAC;wBACAJ;oBACF;oBAEAlC,cACEiC,oBACAc,WACAI,iBACA,OACAX;gBAEJ,SAAU;oBACR,IAAIX,KAAK;wBACP,wEAAwE;wBACxE,gEAAgE;wBAChEU,yBAAAA,MAAOwB,kBAAkB,CACvBhB,WACA,OACAf,MAAMgC,YAAY,EAClB;4BACE,oCAAoC;4BACpC5B,4BAAAA,SAAU6B,MAAM,CAAClC;4BACjB,OAAO;gCACLmC,OAAO1E,4BAA4B2E,mBAAmB;gCACtDC,OAAO;oCAACtC;iCAAK;4BACf;wBACF,GACA,CAACuC;4BACC,OAAO;gCACLC,QAAQ9E,4BAA4B+E,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAE1C,KAAK,oBAAoB,EAAEuC,GAAG;4BAClD;wBACF;wBAEF9B,yBAAAA,MAAOwB,kBAAkB,CACvBjB,WACA,OACAd,MAAMyB,YAAY,EAClB;4BACE,OAAO;gCACLS,OAAO1E,4BAA4BiF,cAAc;4BACnD;wBACF,GACA,CAACJ;4BACC,OAAO;gCACLC,QAAQ9E,4BAA4B+E,WAAW;gCAC/CC,MAAM,CAAC,SAAS,EAAE1C,KAAK,oBAAoB,EAAEuC,GAAG;4BAClD;wBACF;wBAEF,IAAInC,YAAYc,MAAM,CAACQ,QAAQ,EAAE;4BAC/BjB,yBAAAA,MAAOwB,kBAAkB,CACvBrE,YAAY,SAAS,UAAU,cAC/B,OACAwC,YAAYc,MAAM,CAACQ,QAAQ,EAC3B;gCACE,OAAO;oCACLc,QAAQ9E,4BAA4B+E,WAAW;oCAC/CC,MAAM;gCACR;4BACF,GACA,CAACH;gCACC,OAAO;oCACLC,QAAQ9E,4BAA4B+E,WAAW;oCAC/CC,MAAM,CAAC,8CAA8C,EAAEH,GAAG;gCAC5D;4BACF;wBAEJ;oBACF;gBACF;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMnB,MAAMxD,YAAY,SAAS,UAAUoC;gBAE3C,MAAMqB,kBAAkB,MAAMnB,MAAM0C,QAAQ,CAACtB,WAAW;gBACxDb,yBAAAA,MAAOc,qBAAqB,CAACH,KAAKC;gBAElC,MAAMN,OAAOM,gBAAgBN,IAAI;gBAEjC,MAAMV,eAAeoB,iBAAiB,CAACzB;gBACvC,IAAIe,SAAS,QAAQ;oBACnB,MAAMV,eAAeuB,sBAAsB,CAAC5B,MAAM;gBACpD,OAAO;oBACLK,eAAewB,wBAAwB,CAACT;gBAC1C;gBAEA,MAAMf,eAAe2B,cAAc,CAAC;oBAClCzB;oBACAC;oBACAJ;gBACF;gBAEAlC,cAAciC,oBAAoBiB,KAAKC,iBAAiB,MAAMX;gBAE9D;YACF;QACA,KAAK;YAAY;gBACf,MAAMU,MAAMxD,YAAY,OAAO,UAAUoC;gBAEzC,MAAMqB,kBAAkB,MAAMnB,MAAMyB,YAAY,CAACL,WAAW;gBAC5Db,yBAAAA,MAAOc,qBAAqB,CAACH,KAAKC;gBAElC,IAAItB,KAAK;oBACP,wEAAwE;oBACxE,gEAAgE;oBAChEU,yBAAAA,MAAOwB,kBAAkB,CACvBb,KACA,MACAlB,MAAM2C,WAAW,EACjB,CAACC,QAAQC;wBACP,IAAID,OAAOzD,MAAM,CAAC2D,IAAI,CAAC,CAACzE,QAAUA,MAAMC,QAAQ,KAAK,UAAU;4BAC7D,qCAAqC;4BACrC,yDAAyD;4BACzD;wBACF;wBACA,oCAAoC;wBACpC8B,4BAAAA,SAAU6B,MAAM,CAAClC;wBACjB,OAAO;4BACLuC,QAAQ9E,4BAA4BuF,wBAAwB;4BAC5DF;wBACF;oBACF,GACA,CAACR;wBACC,OAAO;4BACLC,QAAQ9E,4BAA4B+E,WAAW;4BAC/CC,MAAM,CAAC,SAAS,EAAE1C,KAAK,wBAAwB,EAAEuC,GAAG;wBACtD;oBACF;gBAEJ;gBAEA,MAAMxB,OAAOM,gBAAgBN,IAAI;gBAEjC,IAAIA,SAAS,QAAQ;oBACnB,MAAMV,eAAeuB,sBAAsB,CAAC5B,MAAM;gBACpD,OAAO;oBACLK,eAAewB,wBAAwB,CAACT;gBAC1C;gBAEA,MAAMf,eAAe6C,oBAAoB,CAAClD;gBAC1C,MAAMK,eAAemB,iBAAiB,CAACxB,MAAM;gBAC7C,MAAMK,eAAe8C,oBAAoB,CAACnD;gBAC1C,MAAMK,eAAe+C,kBAAkB,CAACpD;gBACxC,MAAMK,eAAeyB,gBAAgB,CAAC9B,MAAM;gBAE5C,IAAIW,0BAA0B;oBAC5B,MAAMN,eAAe0B,gBAAgB,CAAC/B,MAAM;gBAC9C;gBAEA,MAAMK,eAAe2B,cAAc,CAAC;oBAClCzB;oBACAC;oBACAJ;gBACF;gBAEAlC,cAAciC,oBAAoBiB,KAAKC,iBAAiBtB,KAAKW;gBAE7D;YACF;QACA,KAAK;YAAa;gBAChB,MAAMU,MAAMxD,YAAY,OAAO,UAAUoC;gBAEzC,MAAMqB,kBAAkB,MAAMnB,MAAM0C,QAAQ,CAACtB,WAAW;gBACxDb,yBAAAA,MAAOc,qBAAqB,CAACH,KAAKC;gBAElC,MAAMN,OAAOM,gBAAgBN,IAAI;gBAEjC,MAAMV,eAAe8C,oBAAoB,CAACnD;gBAE1C,IAAIe,SAAS,QAAQ;oBACnB,MAAMV,eAAeuB,sBAAsB,CAAC5B,MAAM;gBACpD,OAAO;oBACLK,eAAewB,wBAAwB,CAACT;gBAC1C;gBAEA,MAAMf,eAAe2B,cAAc,CAAC;oBAClCzB;oBACAC;oBACAJ;gBACF;gBACAlC,cAAciC,oBAAoBiB,KAAKC,iBAAiB,MAAMX;gBAE9D;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAI2C,MAAM,CAAC,mBAAmB,EAAE,AAACnD,MAAca,IAAI,CAAC,KAAK,EAAEf,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF;AAEA;;CAEC,GACD,OAAO,MAAMsD;IAIX;;;;;GAKC,GACDC,eAAenC,GAAa,EAAEoC,UAAoB,EAAQ;QACxD,IAAI,CAACrB,MAAM,CAACf;QAEZ,MAAMqC,gBAAgB,IAAIpF,IAAImF;QAC9B,IAAI,CAACE,QAAQ,CAACnE,GAAG,CAAC6B,KAAKqC;QAEvB,KAAK,MAAME,aAAaF,cAAe;YACrC,IAAIG,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YACtC,IAAI,CAACC,eAAe;gBAClBA,gBAAgB,IAAIvF;gBACpB,IAAI,CAACwF,QAAQ,CAACtE,GAAG,CAACoE,WAAWC;YAC/B;YAEAA,cAAe/E,GAAG,CAACuC;QACrB;IACF;IAEA;;;;GAIC,GACDe,OAAOf,GAAa,EAAE;QACpB,KAAK,MAAMuC,aAAa,IAAI,CAACI,kBAAkB,CAAC3C,KAAM;YACpD,MAAMwC,gBAAgB,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACH;YAExCC,iCAAAA,cAAezB,MAAM,CAACf;YAEtB,IAAI,EAACwC,iCAAAA,cAAeI,IAAI,GAAE;gBACxB,IAAI,CAACH,QAAQ,CAAC1B,MAAM,CAACwB;YACvB;QACF;QAEA,IAAI,CAACD,QAAQ,CAACvB,MAAM,CAACf;IACvB;IAEA2C,mBAAmB3C,GAAa,EAAY;QAC1C,OAAO6C,MAAMC,IAAI,CAAC,IAAI,CAACR,QAAQ,CAACI,GAAG,CAAC1C,QAAQ,EAAE;IAChD;IAEA+C,eAAeC,IAAY,EAAc;QACvC,OAAOH,MAAMC,IAAI,CAAC,IAAI,CAACL,QAAQ,CAACC,GAAG,CAACM,SAAS,EAAE;IACjD;IAEAC,OAAmC;QACjC,OAAO,IAAI,CAACX,QAAQ,CAACW,IAAI;IAC3B;;aAvDQX,WAAuC,IAAIY;aAC3CT,WAAuC,IAAIS;;AAuDrD;AAEA,OAAO,SAASC,oBACdnE,WAAwB,EACxBgB,GAAa,EACboD,WAAoC;IAEpC,MAAM,EAAEzD,IAAI,EAAEf,IAAI,EAAE,GAAGnC,cAAcuD;IAErC,OAAQL;QACN,KAAK;YACH,OAAOX,YAAYe,GAAG,CAACvC,GAAG,CAACoB;QAC7B,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOI,YAAYc,MAAM,CAACC,GAAG,IAAI;gBACnC,KAAK;oBACH,OAAOf,YAAYc,MAAM,CAACQ,QAAQ,IAAI;gBACxC,KAAK;oBACH,OAAOtB,YAAYc,MAAM,CAACuD,KAAK,IAAI;gBACrC;oBACE,OAAOrE,YAAYJ,IAAI,CAACpB,GAAG,CAACoB;YAChC;QACF,KAAK;YACH,OAAQA;gBACN,KAAK;oBACH,OAAOI,YAAYc,MAAM,CAACwD,UAAU,IAAI;gBAC1C,KAAK;oBACH,OAAOtE,YAAYc,MAAM,CAACyD,eAAe,IAAI;gBAC/C;oBACE,OAAO;YACX;QACF,KAAK;YACH,IAAI,CAACH,aAAa;gBAChB,OAAO;YACT;YAEA,OAAOA,YACJL,cAAc,CAACnE,MACfgD,IAAI,CAAC,CAAC4B,UACLL,oBAAoBnE,aAAawE,SAASJ;QAEhD;YAAS;gBACP,+DAA+D;gBAC/D,6DAA6D;gBAC7D,MAAMK,IAAW9D;gBACjB,OAAO;YACT;IACF;AACF;AA0BA,OAAO,eAAe+D,kBAAkB,EACtC1E,WAAW,EAEX2E,kBAAkB,EAElB5E,kBAAkB,EAClBE,cAAc,EACdE,WAAW,EACXG,SAAS,EACTX,GAAG,EAaJ;IACCgF,mBAAmB7D,MAAM,CAACC,GAAG,GAAGf,YAAY4E,gBAAgB;IAC5DD,mBAAmB7D,MAAM,CAACQ,QAAQ,GAAGtB,YAAY6E,qBAAqB;IACtEF,mBAAmB7D,MAAM,CAACuD,KAAK,GAAGrE,YAAY8E,kBAAkB;IAEhEH,mBAAmB7D,MAAM,CAACyD,eAAe,GAAGvE,YAAYuE,eAAe;IAEvEI,mBAAmB/E,IAAI,CAACZ,KAAK;IAC7B2F,mBAAmB5D,GAAG,CAAC/B,KAAK;IAE5B,KAAK,MAAM,CAACa,UAAUC,MAAM,IAAIE,YAAY+E,MAAM,CAAE;QAClD,OAAQjF,MAAMa,IAAI;YAChB,KAAK;YACL,KAAK;gBACHgE,mBAAmB/E,IAAI,CAACT,GAAG,CAACU,UAAUC;gBACtC;YACF,KAAK;gBAAY;oBACfA,MAAMoC,KAAK,CAAC8C,OAAO,CAAC,CAACpF;wBACnB+E,mBAAmB5D,GAAG,CAAC5B,GAAG,CAACS,KAAKqF,YAAY,EAAE;4BAC5CtE,MAAM;4BACN,GAAGf,IAAI;wBACT;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChB+E,mBAAmB5D,GAAG,CAAC5B,GAAG,CAACW,MAAMmF,YAAY,EAAEnF;oBAC/C;gBACF;YACA;gBACEvC,IAAI2H,IAAI,CAAC,CAAC,SAAS,EAAErF,SAAS,EAAE,EAAEC,MAAMa,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,IAAIhB,KAAK;QACP,MAAMwF,4BAA4B;YAChCpF;YACA4E;YAEA,GAAGhF,GAAG;QACR;IACF;IAEA,MAAM,EAAE2E,UAAU,EAAEC,eAAe,EAAE,GAAGvE;IAExC,8DAA8D;IAC9D,8DAA8D;IAC9D,sCAAsC;IACtC,IAAI2E,mBAAmB7D,MAAM,CAACwD,UAAU,IAAI,CAACA,YAAY;QACvD,MAAMtD,MAAMxD,YAAY,QAAQ,UAAU;QAC1C,wCAAwC;QACxC,OAAMmC,uBAAAA,IAAKU,KAAK,CAAC+E,sBAAsB,CAACpE;QACxCjB,mBAAmBgC,MAAM,CAACf;QAC1BrB,IAAIU,KAAK,CAACgF,OAAO,CAAC,cAAc;YAC9BrD,OAAO1E,4BAA4BgI,kBAAkB;QACvD;IACF,OAAO,IAAI,CAACX,mBAAmB7D,MAAM,CAACwD,UAAU,IAAIA,YAAY;QAC9D,wCAAwC;QACxC3E,IAAIU,KAAK,CAACgF,OAAO,CAAC,cAAc;YAC9BrD,OAAO1E,4BAA4BgI,kBAAkB;QACvD;IACF;IAEAX,mBAAmB7D,MAAM,CAACwD,UAAU,GAAGA;IAEvC,IAAIC,iBAAiB;QACnB,MAAMgB,yBAAyB,OAC7BC,MACAC;YAEA,MAAMzE,MAAMxD,YAAY,QAAQ,UAAUgI;YAE1C,MAAMvE,kBAAkB,MAAMsD,eAAe,CAACkB,KAAK,CAACvE,WAAW;YAC/DvB,IAAIU,KAAK,CAACc,qBAAqB,CAACH,KAAKC;YACrCnD,cAAciC,oBAAoBiB,KAAKC,iBAAiB,OAAOX;QACjE;QACA,MAAMiF,uBAAuB,0BAA0B;QACvD,MAAMA,uBAAuB,wBAAwB;QACrD,MAAMtF,eAAeuB,sBAAsB,CACzC,mBACA;QAEF,MAAMvB,eAAe2B,cAAc,CAAC;YAClCzB;YACAC,oBAAoBsF;YACpB1F,aAAa2E;QACf;QAEAhF,IAAIgG,YAAY,CAACC,6BAA6B,GAAG;QACjD,MAAMjG,IAAIU,KAAK,CAACwF,oBAAoB,CAClC,iCACAlG,IAAIgG,YAAY,CAACC,6BAA6B;IAElD,OAAO;QACLjG,IAAIgG,YAAY,CAACC,6BAA6B,GAAGF;QACjD,MAAM/F,IAAIU,KAAK,CAACwF,oBAAoB,CAClC,iCACAlG,IAAIgG,YAAY,CAACC,6BAA6B;IAElD;IAEA,IAAItB,YAAY;QACd,MAAMtD,MAAMxD,YAAY,QAAQ,UAAU;QAE1C,MAAMgF,WAAW8B,WAAW9B,QAAQ;QAEpC,eAAesD;gBAMX7F;YALF,MAAMgB,kBAAkB,MAAMuB,SAAStB,WAAW;YAClDvB,IAAIU,KAAK,CAACc,qBAAqB,CAACH,KAAKC;YACrCnD,cAAciC,oBAAoBiB,KAAKC,iBAAiB,OAAOX;YAC/D,MAAML,eAAeuB,sBAAsB,CAAC,cAAc;YAC1D,MAAMuE,oBACJ9F,wCAAAA,eAAe+F,qBAAqB,CAAChF,yBAArCf,sCAA2CqE,UAAU,CAAC,IAAI;YAE5D,IAAI3E,OAAOoG,kBAAkB;gBAC3BpG,IAAIgG,YAAY,CAACrB,UAAU,GAAG;oBAC5B2B,OAAO;oBACPrG,MAAM;oBACNsG,UAAUH,iBAAiBG,QAAQ;gBACrC;YACF;QACF;QACA,MAAMJ;QAEN,IAAInG,KAAK;YACPA,uBAAAA,IAAKU,KAAK,CAACwB,kBAAkB,CAC3Bb,KACA,OACAwB,UACA;gBACE,MAAM2D,iBAAiBxG,IAAIU,KAAK,CAAC+F,aAAa,CAC5C,cACAV,WACA;gBAEF,MAAMI;gBACN,MAAMnG,IAAIU,KAAK,CAACwF,oBAAoB,CAClC,wBACAlG,IAAIgG,YAAY,CAACU,oBAAoB;gBAEvC,MAAM1G,IAAIU,KAAK,CAACwF,oBAAoB,CAClC,cACAlG,IAAIgG,YAAY,CAACrB,UAAU;gBAE7B,MAAMrE,eAAe2B,cAAc,CAAC;oBAClCzB;oBACAC,oBAAoBsF;oBACpB1F,aAAa2E;gBACf;gBAEAwB,kCAAAA;gBACA,OAAO;oBAAEnE,OAAO1E,4BAA4BgI,kBAAkB;gBAAC;YACjE,GACA;gBACE,OAAO;oBACLtD,OAAO1E,4BAA4BgI,kBAAkB;gBACvD;YACF;QAEJ;IACF,OAAO;QACLrF,eAAewB,wBAAwB,CACrCjE,YAAY,QAAQ,UAAU;QAEhCmC,IAAIgG,YAAY,CAACU,oBAAoB,GAAGX;QACxC/F,IAAIgG,YAAY,CAACrB,UAAU,GAAGoB;IAChC;IAEA,MAAM/F,IAAIU,KAAK,CAACwF,oBAAoB,CAClC,wBACAlG,IAAIgG,YAAY,CAACU,oBAAoB;IAEvC,MAAM1G,IAAIU,KAAK,CAACwF,oBAAoB,CAClC,cACAlG,IAAIgG,YAAY,CAACrB,UAAU;AAE/B;AAEA,eAAea,4BAA4B,EACzCpF,kBAAkB,EAClB4E,kBAAkB,EAElBP,WAAW,EACXkC,mBAAmB,EACnBC,OAAO,EACPC,YAAY,EAEZnG,KAAK,EAIqB;IAC1B,yEAAyE;IACzE,KAAK,MAAMW,OAAOoD,YAAYH,IAAI,GAAI;QACpC,IAAI,CAACE,oBAAoBQ,oBAAoB3D,KAAKoD,cAAc;YAC9DA,YAAYrC,MAAM,CAACf;QACrB;IACF;IAEA,KAAK,MAAMA,OAAOsF,oBAAoBrC,IAAI,GAAI;QAC5C,mCAAmC;QACnC,IAAI,CAACE,oBAAoBQ,oBAAoB3D,KAAKoD,cAAc;YAC9D,MAAM/D,MAAM+E,sBAAsB,CAACpE;QACrC;IACF;IAEA,KAAK,MAAM,CAACA,IAAI,IAAIjB,mBAAoB;QACtC,IAAI,CAACoE,oBAAoBQ,oBAAoB3D,KAAKoD,cAAc;YAC9DrE,mBAAmBgC,MAAM,CAACf;QAC5B;IACF;IAEA,KAAK,MAAMyF,UAAUF,QAAS;QAC5B,MAAMG,QAAQF,aAAa9C,GAAG,CAAC+C;QAC/B,IAAI,CAACC,OAAO;YACV;QACF;QAEA,KAAK,MAAM1F,OAAO0F,MAAMC,YAAY,CAAC1C,IAAI,GAAI;YAC3C,IAAI,CAACE,oBAAoBQ,oBAAoB3D,KAAKoD,cAAc;gBAC9DsC,MAAMC,YAAY,CAAC5E,MAAM,CAACf;YAC5B;QACF;QAEA,KAAK,MAAM4F,MAAMF,MAAMG,aAAa,CAAC5C,IAAI,GAAI;YAC3C,IACE,CAACE,oBACCQ,oBACAnH,YAAY,UAAU,UAAUoJ,KAChCxC,cAEF;gBACA/D,MAAMyG,wBAAwB,CAACL,QAAQG;YACzC;QACF;IACF;AACF;AAEA,OAAO,eAAeG,sBAAsB,EAC1ChH,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdE,WAAW,EACXC,kBAAkB,EAClBE,SAAS,EACTD,KAAK,EASN;IACC,IAAIL,YAAYc,MAAM,CAACC,GAAG,EAAE;QAC1B,MAAMC,MAAMxD,YAAY,SAAS,UAAU;QAE3C,MAAMyD,kBAAkB,MAAMjB,YAAYc,MAAM,CAACC,GAAG,CAACG,WAAW;QAChEb,MAAMc,qBAAqB,CAACH,KAAKC;QACjCZ,MAAMwB,kBAAkB,CACtBb,KACA,OACAhB,YAAYc,MAAM,CAACC,GAAG,EACtB;YACE,oEAAoE;YACpE,qIAAqI;YACrI,OAAO;gBAAEiB,OAAO1E,4BAA4BiF,cAAc;YAAC;QAC7D,GACA;YACE,OAAO;gBACLH,QAAQ9E,4BAA4B+E,WAAW;gBAC/CC,MAAM;YACR;QACF;QAEFxE,cAAciC,oBAAoBiB,KAAKC,iBAAiB,OAAOX;IACjE;IACA,MAAML,eAAemB,iBAAiB,CAAC;IACvC,MAAMnB,eAAeoB,iBAAiB,CAAC;IACvC,MAAMpB,eAAeyB,gBAAgB,CAAC;IAEtC,IAAI1B,YAAYc,MAAM,CAACQ,QAAQ,EAAE;QAC/B,MAAMN,MAAMxD,YAAY,SAAS,UAAU;QAE3C,MAAMyD,kBAAkB,MAAMjB,YAAYc,MAAM,CAACQ,QAAQ,CAACJ,WAAW;QACrEb,MAAMc,qBAAqB,CAACH,KAAKC;QACjCZ,MAAMwB,kBAAkB,CACtBb,KACA,OACAhB,YAAYc,MAAM,CAACQ,QAAQ,EAC3B;YACE,OAAO;gBACLc,QAAQ9E,4BAA4B+E,WAAW;gBAC/CC,MAAM;YACR;QACF,GACA,CAACH;YACC,OAAO;gBACLC,QAAQ9E,4BAA4B+E,WAAW;gBAC/CC,MAAM,CAAC,+CAA+C,EAAEH,GAAG;YAC7D;QACF;QAEFrE,cAAciC,oBAAoBiB,KAAKC,iBAAiB,OAAOX;IACjE;IACA,MAAML,eAAeoB,iBAAiB,CAAC;IAEvC,IAAIrB,YAAYc,MAAM,CAACuD,KAAK,EAAE;QAC5B,MAAMrD,MAAMxD,YAAY,SAAS,UAAU;QAE3C,MAAMyD,kBAAkB,MAAMjB,YAAYc,MAAM,CAACuD,KAAK,CAACnD,WAAW;QAClEb,MAAMc,qBAAqB,CAACH,KAAKC;QACjCZ,MAAMwB,kBAAkB,CACtBb,KACA,OACAhB,YAAYc,MAAM,CAACuD,KAAK,EACxB;YACE,oEAAoE;YACpE,qIAAqI;YACrI,OAAO;gBAAErC,OAAO1E,4BAA4BiF,cAAc;YAAC;QAC7D,GACA,CAACJ;YACC,OAAO;gBACLC,QAAQ9E,4BAA4B+E,WAAW;gBAC/CC,MAAM,CAAC,8BAA8B,EAAEH,GAAG;YAC5C;QACF;QAEFrE,cAAciC,oBAAoBiB,KAAKC,iBAAiB,OAAOX;IACjE;IACA,MAAML,eAAemB,iBAAiB,CAAC;IACvC,MAAMnB,eAAeoB,iBAAiB,CAAC;IACvC,MAAMpB,eAAeyB,gBAAgB,CAAC;IAEtC,MAAMzB,eAAe2B,cAAc,CAAC;QAClCzB;QACAC;QACAJ;IACF;AACF;AAEA,OAAO,SAASgH,kBAAkBlH,KAAa;IAC7C,OAAOA,MAAMmH,OAAO,CAAC,YAAY;AACnC;AAEA,OAAO,SAASC,eAAepH,KAAa;IAC1C,OAAOA,QAAQ;AACjB;AAEA,OAAO,SAASqH,qBAAqBrH,KAAa;IAChD,OAAOA,QAAQ;AACjB;AAEA,8EAA8E;AAC9E,sDAAsD;AACtD,2FAA2F;AAC3F,mFAAmF;AACnF,OAAO,SAASsH,wCACdtH,KAAa,EACbuH,GAAmB;IAEnB,IAAIC,gBAAgBxH;IACpB,IAAIpC,gBAAgB4J,gBAAgB;QAClCA,gBAAgBA,cAAcC,QAAQ,CAAC,YACnCD,cAAcE,KAAK,CAAC,GAAG,CAAC,SAASC,MAAM,IACvCH;QAEJ,IAAID,KAAK;YACP,IAAIC,cAAcC,QAAQ,CAAC,uBAAuB;gBAChDD,gBAAgBA,cAAcE,KAAK,CAAC,GAAG,CAAC,qBAAqBC,MAAM;YACrE;YACA,IAAIH,cAAcC,QAAQ,CAAC,mBAAmBF,QAAQ,QAAQ;gBAC5D,kDAAkD;gBAClDC,gBAAgBA,cAAcE,KAAK,CAAC,GAAG,CAAC,OAAOC,MAAM;YACvD;QACF;QACAH,gBAAgBA,gBAAgB;IAClC;IACA,OAAOA;AACT"}