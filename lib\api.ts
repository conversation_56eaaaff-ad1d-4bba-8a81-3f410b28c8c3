import { 
  Order, 
  Dealer, 
  Quota, 
  LogisticsInfo, 
  TodoItem, 
  ApiResponse, 
  PaginationParams, 
  SearchParams 
} from './types'
import { 
  mockOrders, 
  mockDealers, 
  mockQuotas, 
  mockLogistics, 
  mockTodos 
} from './mock-data'

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 订单相关API
export const orderApi = {
  // 获取订单列表
  async getOrders(params: SearchParams & PaginationParams): Promise<ApiResponse<{ orders: Order[], total: number }>> {
    await delay(500)
    
    let filteredOrders = [...mockOrders]
    
    // 搜索过滤
    if (params.keyword) {
      filteredOrders = filteredOrders.filter(order => 
        order.orderNumber.toLowerCase().includes(params.keyword!.toLowerCase()) ||
        order.customerName.toLowerCase().includes(params.keyword!.toLowerCase()) ||
        order.dealerName.toLowerCase().includes(params.keyword!.toLowerCase())
      )
    }
    
    // 状态过滤
    if (params.status) {
      filteredOrders = filteredOrders.filter(order => order.status === params.status)
    }
    
    // 经销商过滤
    if (params.dealerId) {
      filteredOrders = filteredOrders.filter(order => order.dealerId === params.dealerId)
    }
    
    // 分页
    const start = (params.page - 1) * params.pageSize
    const end = start + params.pageSize
    const paginatedOrders = filteredOrders.slice(start, end)
    
    return {
      success: true,
      data: {
        orders: paginatedOrders,
        total: filteredOrders.length
      }
    }
  },

  // 获取单个订单详情
  async getOrder(id: string): Promise<ApiResponse<Order>> {
    await delay(300)
    
    const order = mockOrders.find(o => o.id === id)
    if (!order) {
      return {
        success: false,
        error: '订单不存在'
      }
    }
    
    return {
      success: true,
      data: order
    }
  },

  // 切换订单经销商
  async switchOrderDealer(orderId: string, newDealerId: string): Promise<ApiResponse<Order>> {
    await delay(800)
    
    const orderIndex = mockOrders.findIndex(o => o.id === orderId)
    if (orderIndex === -1) {
      return {
        success: false,
        error: '订单不存在'
      }
    }
    
    const newDealer = mockDealers.find(d => d.id === newDealerId)
    if (!newDealer) {
      return {
        success: false,
        error: '经销商不存在'
      }
    }
    
    // 检查订单是否可以切换
    if (!mockOrders[orderIndex].canSwitch) {
      return {
        success: false,
        error: '该订单不允许切换经销商'
      }
    }
    
    // 更新订单
    mockOrders[orderIndex] = {
      ...mockOrders[orderIndex],
      dealerId: newDealerId,
      dealerName: newDealer.name,
      updatedAt: new Date().toISOString()
    }
    
    return {
      success: true,
      data: mockOrders[orderIndex],
      message: '订单经销商切换成功'
    }
  }
}

// 经销商相关API
export const dealerApi = {
  // 获取经销商列表
  async getDealers(params: SearchParams): Promise<ApiResponse<Dealer[]>> {
    await delay(300)
    
    let filteredDealers = [...mockDealers]
    
    if (params.keyword) {
      filteredDealers = filteredDealers.filter(dealer => 
        dealer.name.toLowerCase().includes(params.keyword!.toLowerCase()) ||
        dealer.code.toLowerCase().includes(params.keyword!.toLowerCase()) ||
        dealer.region.toLowerCase().includes(params.keyword!.toLowerCase())
      )
    }
    
    return {
      success: true,
      data: filteredDealers
    }
  },

  // 获取单个经销商详情
  async getDealer(id: string): Promise<ApiResponse<Dealer>> {
    await delay(200)
    
    const dealer = mockDealers.find(d => d.id === id)
    if (!dealer) {
      return {
        success: false,
        error: '经销商不存在'
      }
    }
    
    return {
      success: true,
      data: dealer
    }
  }
}

// 配额相关API
export const quotaApi = {
  // 获取配额列表
  async getQuotas(params: SearchParams): Promise<ApiResponse<Quota[]>> {
    await delay(400)
    
    let filteredQuotas = [...mockQuotas]
    
    if (params.dealerId) {
      filteredQuotas = filteredQuotas.filter(quota => quota.dealerId === params.dealerId)
    }
    
    return {
      success: true,
      data: filteredQuotas
    }
  }
}

// 物流相关API
export const logisticsApi = {
  // 获取物流信息
  async getLogistics(orderId: string): Promise<ApiResponse<LogisticsInfo>> {
    await delay(600)
    
    const logistics = mockLogistics.find(l => l.orderId === orderId)
    if (!logistics) {
      return {
        success: false,
        error: '物流信息不存在'
      }
    }
    
    return {
      success: true,
      data: logistics
    }
  }
}

// 待办事项相关API
export const todoApi = {
  // 获取待办事项列表
  async getTodos(): Promise<ApiResponse<TodoItem[]>> {
    await delay(300)
    
    return {
      success: true,
      data: mockTodos
    }
  },

  // 完成待办事项
  async completeTodo(id: string): Promise<ApiResponse<TodoItem>> {
    await delay(400)
    
    const todoIndex = mockTodos.findIndex(t => t.id === id)
    if (todoIndex === -1) {
      return {
        success: false,
        error: '待办事项不存在'
      }
    }
    
    mockTodos[todoIndex] = {
      ...mockTodos[todoIndex],
      status: 'completed'
    }
    
    return {
      success: true,
      data: mockTodos[todoIndex],
      message: '待办事项已完成'
    }
  }
}
