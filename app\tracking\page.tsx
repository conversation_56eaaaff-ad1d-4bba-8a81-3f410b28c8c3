"use client"

import { useState, useEffect } from 'react'
import { ArrowLeft, Search, Package, Truck, CheckCircle, Clock, AlertTriangle, Phone } from "lucide-react"
import Link from "next/link"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from '@/hooks/use-toast'
import { LogisticsInfo, Order } from '@/lib/types'
import { logisticsApi, orderApi } from '@/lib/api'

export default function TrackingPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [logisticsInfo, setLogisticsInfo] = useState<LogisticsInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [orders, setOrders] = useState<Order[]>([])
  const { toast } = useToast()

  // 获取物流状态显示文本
  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      picked_up: '已揽收',
      in_transit: '运输中',
      out_for_delivery: '派送中',
      delivered: '已送达',
      exception: '异常'
    }
    return statusMap[status] || status
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'picked_up':
        return <Package className="w-5 h-5 text-blue-600" />
      case 'in_transit':
        return <Truck className="w-5 h-5 text-blue-600" />
      case 'out_for_delivery':
        return <Truck className="w-5 h-5 text-orange-600" />
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'exception':
        return <AlertTriangle className="w-5 h-5 text-red-600" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      picked_up: 'bg-blue-100 text-blue-800',
      in_transit: 'bg-blue-100 text-blue-800',
      out_for_delivery: 'bg-orange-100 text-orange-800',
      delivered: 'bg-green-100 text-green-800',
      exception: 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  // 搜索物流信息
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: '请输入搜索内容',
        description: '请输入订单号或快递单号',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)
    try {
      // 首先尝试通过订单号搜索
      const ordersResponse = await orderApi.getOrders({
        keyword: searchQuery,
        page: 1,
        pageSize: 10
      })

      if (ordersResponse.success && ordersResponse.data && ordersResponse.data.orders.length > 0) {
        const order = ordersResponse.data.orders[0]

        // 获取物流信息
        const logisticsResponse = await logisticsApi.getLogistics(order.id)

        if (logisticsResponse.success && logisticsResponse.data) {
          setLogisticsInfo(logisticsResponse.data)
        } else {
          toast({
            title: '未找到物流信息',
            description: '该订单暂无物流信息',
            variant: 'destructive'
          })
        }
      } else {
        toast({
          title: '未找到订单',
          description: '请检查订单号是否正确',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: '搜索失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 加载最近订单
  const loadRecentOrders = async () => {
    try {
      const response = await orderApi.getOrders({
        page: 1,
        pageSize: 5
      })

      if (response.success && response.data) {
        setOrders(response.data.orders)
      }
    } catch (error) {
      console.error('Failed to load recent orders:', error)
    }
  }

  // 快速查询订单物流
  const quickTrack = async (orderId: string) => {
    setLoading(true)
    try {
      const response = await logisticsApi.getLogistics(orderId)

      if (response.success && response.data) {
        setLogisticsInfo(response.data)
        setSearchQuery(response.data.trackingNumber)
      } else {
        toast({
          title: '未找到物流信息',
          description: '该订单暂无物流信息',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: '查询失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRecentOrders()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center">
        <Link href="/">
          <ArrowLeft className="w-6 h-6 mr-4" />
        </Link>
        <h1 className="text-xl font-semibold">物流跟踪</h1>
      </div>

      <div className="p-4 space-y-4">
        {/* Search */}
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              placeholder="输入订单号或快递单号"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="pl-10"
            />
          </div>
          <Button onClick={handleSearch} disabled={loading}>
            {loading ? '搜索中...' : '搜索'}
          </Button>
        </div>

        {/* 最近订单快速查询 */}
        {!logisticsInfo && orders.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">最近订单</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {orders.map((order) => (
                  <div key={order.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium">{order.orderNumber}</div>
                      <div className="text-sm text-gray-600">{order.customerName}</div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => quickTrack(order.id)}
                    >
                      查询物流
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 物流信息 */}
        {logisticsInfo && (
          <div className="space-y-4">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">物流信息</CardTitle>
                    <p className="text-sm text-gray-600">快递单号: {logisticsInfo.trackingNumber}</p>
                  </div>
                  <Badge className={getStatusColor(logisticsInfo.status)}>
                    {getStatusText(logisticsInfo.status)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">承运商:</span>
                    <span>{logisticsInfo.carrier}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">当前位置:</span>
                    <span>{logisticsInfo.currentLocation}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">预计送达:</span>
                    <span>{new Date(logisticsInfo.estimatedDelivery).toLocaleString()}</span>
                  </div>
                  {logisticsInfo.actualDelivery && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">实际送达:</span>
                      <span>{new Date(logisticsInfo.actualDelivery).toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 物流轨迹 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">物流轨迹</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {logisticsInfo.events.map((event, index) => (
                    <div key={event.id} className="flex items-start gap-4">
                      <div className="flex flex-col items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          {getStatusIcon(event.status)}
                        </div>
                        {index < logisticsInfo.events.length - 1 && (
                          <div className="w-0.5 h-8 bg-gray-200 mt-2"></div>
                        )}
                      </div>
                      <div className="flex-1 pb-4">
                        <div className="font-medium">{event.description}</div>
                        <div className="text-sm text-gray-600">{new Date(event.timestamp).toLocaleString()}</div>
                        <div className="text-sm text-gray-600">{event.location}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <Button variant="outline" className="flex-1">
                <Phone className="w-4 h-4 mr-2" />
                联系快递员
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setLogisticsInfo(null)
                  setSearchQuery('')
                }}
              >
                重新查询
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="grid grid-cols-4">
          <Link href="/" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/tasks" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">✓</div>
            <span className="text-xs">任务</span>
          </Link>
          <Link href="/order-switch" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🔄</div>
            <span className="text-xs">订单切换</span>
          </Link>
          <Link href="/quota" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">📊</div>
            <span className="text-xs">配额</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
