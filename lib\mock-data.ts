import { Dealer, Order, OrderProduct, Quota, LogisticsInfo, LogisticsEvent, TodoItem } from './types'

// Mock 经销商数据
export const mockDealers: Dealer[] = [
  {
    id: 'dealer-001',
    name: '北京华联经销商',
    code: 'BJ001',
    region: '华北',
    contact: '张经理',
    phone: '13800138001',
    email: '<EMAIL>',
    address: '北京市朝阳区建国路88号',
    status: 'active',
    creditLimit: 1000000,
    currentCredit: 750000
  },
  {
    id: 'dealer-002',
    name: '上海浦东销售中心',
    code: 'SH001',
    region: '华东',
    contact: '李总监',
    phone: '13800138002',
    email: '<EMAIL>',
    address: '上海市浦东新区陆家嘴金融中心',
    status: 'active',
    creditLimit: 1500000,
    currentCredit: 1200000
  },
  {
    id: 'dealer-003',
    name: '广州南方贸易公司',
    code: 'GZ001',
    region: '华南',
    contact: '王主任',
    phone: '13800138003',
    email: '<EMAIL>',
    address: '广州市天河区珠江新城',
    status: 'active',
    creditLimit: 800000,
    currentCredit: 600000
  },
  {
    id: 'dealer-004',
    name: '成都西部代理商',
    code: 'CD001',
    region: '西南',
    contact: '刘经理',
    phone: '13800138004',
    email: '<EMAIL>',
    address: '成都市高新区天府大道',
    status: 'active',
    creditLimit: 600000,
    currentCredit: 400000
  }
]

// Mock 订单产品数据
const mockOrderProducts: OrderProduct[] = [
  {
    id: 'product-001',
    name: '智能手机 Pro Max',
    sku: 'PHONE-001',
    quantity: 2,
    unitPrice: 8999,
    totalPrice: 17998
  },
  {
    id: 'product-002',
    name: '无线耳机',
    sku: 'EARPHONE-001',
    quantity: 5,
    unitPrice: 1299,
    totalPrice: 6495
  }
]

// Mock 订单数据
export const mockOrders: Order[] = [
  {
    id: 'order-001',
    orderNumber: 'ORD-2024-001',
    dealerId: 'dealer-001',
    dealerName: '北京华联经销商',
    customerName: '张三',
    customerPhone: '13900139001',
    products: mockOrderProducts,
    totalAmount: 24493,
    status: 'confirmed',
    createdAt: '2024-12-18T10:00:00Z',
    updatedAt: '2024-12-18T14:30:00Z',
    deliveryAddress: '北京市海淀区中关村大街1号',
    notes: '客户要求加急处理',
    canSwitch: true
  },
  {
    id: 'order-002',
    orderNumber: 'ORD-2024-002',
    dealerId: 'dealer-002',
    dealerName: '上海浦东销售中心',
    customerName: '李四',
    customerPhone: '13900139002',
    products: [mockOrderProducts[0]],
    totalAmount: 8999,
    status: 'pending',
    createdAt: '2024-12-19T09:15:00Z',
    updatedAt: '2024-12-19T09:15:00Z',
    deliveryAddress: '上海市浦东新区世纪大道100号',
    canSwitch: true
  },
  {
    id: 'order-003',
    orderNumber: 'ORD-2024-003',
    dealerId: 'dealer-003',
    dealerName: '广州南方贸易公司',
    customerName: '王五',
    customerPhone: '13900139003',
    products: [mockOrderProducts[1]],
    totalAmount: 1299,
    status: 'processing',
    createdAt: '2024-12-17T16:20:00Z',
    updatedAt: '2024-12-18T08:45:00Z',
    deliveryAddress: '广州市天河区体育西路200号',
    canSwitch: false
  }
]

// Mock 配额数据
export const mockQuotas: Quota[] = [
  {
    id: 'quota-001',
    dealerId: 'dealer-001',
    dealerName: '北京华联经销商',
    productCategory: '智能手机',
    allocatedAmount: 500000,
    usedAmount: 350000,
    remainingAmount: 150000,
    period: '2024-Q4',
    status: 'confirmed',
    createdAt: '2024-10-01T00:00:00Z',
    updatedAt: '2024-12-19T10:00:00Z'
  },
  {
    id: 'quota-002',
    dealerId: 'dealer-002',
    dealerName: '上海浦东销售中心',
    productCategory: '智能手机',
    allocatedAmount: 800000,
    usedAmount: 600000,
    remainingAmount: 200000,
    period: '2024-Q4',
    status: 'confirmed',
    createdAt: '2024-10-01T00:00:00Z',
    updatedAt: '2024-12-19T10:00:00Z'
  }
]

// Mock 物流事件数据
const mockLogisticsEvents: LogisticsEvent[] = [
  {
    id: 'event-001',
    timestamp: '2024-12-18T15:00:00Z',
    location: '北京分拣中心',
    description: '包裹已从发货地发出',
    status: 'picked_up'
  },
  {
    id: 'event-002',
    timestamp: '2024-12-18T20:30:00Z',
    location: '天津中转站',
    description: '包裹正在运输途中',
    status: 'in_transit'
  }
]

// Mock 物流信息数据
export const mockLogistics: LogisticsInfo[] = [
  {
    id: 'logistics-001',
    orderId: 'order-001',
    trackingNumber: 'SF1234567890',
    carrier: '顺丰速运',
    status: 'in_transit',
    currentLocation: '天津中转站',
    estimatedDelivery: '2024-12-20T18:00:00Z',
    events: mockLogisticsEvents
  }
]

// Mock 待办事项数据
export const mockTodos: TodoItem[] = [
  {
    id: 'todo-001',
    type: 'approval',
    title: '审批销售订单',
    description: '订单 #ORD-2024-002 等待审批',
    priority: 'high',
    status: 'pending',
    dueDate: '2024-12-20T18:00:00Z',
    createdAt: '2024-12-19T09:15:00Z',
    relatedId: 'order-002'
  },
  {
    id: 'todo-002',
    type: 'task',
    title: '跟进客户',
    description: '联系张三确认收货地址',
    priority: 'medium',
    status: 'pending',
    dueDate: '2024-12-21T12:00:00Z',
    createdAt: '2024-12-18T10:00:00Z',
    relatedId: 'order-001'
  },
  {
    id: 'todo-003',
    type: 'notification',
    title: '新政策更新',
    description: '2025年销售政策已发布，请及时查看',
    priority: 'medium',
    status: 'pending',
    createdAt: '2024-12-19T08:00:00Z'
  }
]
