// 经销商信息类型
export interface Dealer {
  id: string
  name: string
  code: string
  region: string
  contact: string
  phone: string
  email: string
  address: string
  status: 'active' | 'inactive'
  creditLimit: number
  currentCredit: number
}

// 订单状态类型
export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'

// 订单类型
export interface Order {
  id: string
  orderNumber: string
  dealerId: string
  dealerName: string
  customerName: string
  customerPhone: string
  products: OrderProduct[]
  totalAmount: number
  status: OrderStatus
  createdAt: string
  updatedAt: string
  deliveryAddress: string
  notes?: string
  canSwitch: boolean // 是否可以切换经销商
}

// 订单产品类型
export interface OrderProduct {
  id: string
  name: string
  sku: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

// 配额类型
export interface Quota {
  id: string
  dealerId: string
  dealerName: string
  productCategory: string
  allocatedAmount: number
  usedAmount: number
  remainingAmount: number
  period: string // 例如: "2024-Q1"
  status: 'draft' | 'pending' | 'confirmed' | 'adjusted'
  createdAt: string
  updatedAt: string
}

// 物流跟踪信息类型
export interface LogisticsInfo {
  id: string
  orderId: string
  trackingNumber: string
  carrier: string
  status: 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception'
  currentLocation: string
  estimatedDelivery: string
  actualDelivery?: string
  events: LogisticsEvent[]
}

// 物流事件类型
export interface LogisticsEvent {
  id: string
  timestamp: string
  location: string
  description: string
  status: string
}

// 待办事项类型
export interface TodoItem {
  id: string
  type: 'approval' | 'task' | 'notification'
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'completed' | 'overdue'
  dueDate?: string
  createdAt: string
  relatedId?: string // 关联的订单ID、配额ID等
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页参数类型
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 搜索参数类型
export interface SearchParams {
  keyword?: string
  status?: string
  dateFrom?: string
  dateTo?: string
  dealerId?: string
}
